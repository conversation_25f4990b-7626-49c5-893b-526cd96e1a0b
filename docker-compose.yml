version: "3.7"

services:
  supertokens:
    image: supertokens/supertokens-postgresql
    container_name: supertokens
    restart: unless-stopped
    ports:
      - "3567:3567"
    environment:
      POSTGRESQL_USER: dev_db_pg_owner
      POSTGRESQL_PASSWORD: Ce3Y5YqA78qGH@E_cadet_root
      POSTGRESQL_HOST: cl-pab-dev-sindia-db-1.postgres.database.azure.com
      POSTGRESQL_PORT: 5432
      POSTGRESQL_DATABASE_NAME: dev
      POSTGRESQL_TABLE_SCHEMA: auth
# postgresql://citus:<EMAIL>/citus?statusColor=686B6F&env=&name=aazure&tLSMode=0&usePrivateKey=false&safeModeLevel=0&advancedSafeModeLevel=0&driverVersion=0&lazyload=false
