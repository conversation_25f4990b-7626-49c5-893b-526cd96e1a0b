"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Session from "supertokens-auth-react/recipe/session";
import Loading from "@repo/ui/components/loading";

export default function HomePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function checkAuth() {
      const isLoggedIn = await Session.doesSessionExist();
      if (isLoggedIn) {
        router.replace("/dashboard");
      } else {
        router.replace("/auth");
      }
      setLoading(false);
    }
    checkAuth();
  }, [router]);

  if (loading) {
    return <Loading />;
  }

  return null;
}
