"use client";
import React, { ReactNode } from "react";
import { useAccessContext, hasAccess } from "@/components/access-provider";

// 🔑 CanAccess component
interface CanAccessProps {
  resource: string;
  privilege: "create" | "update" | "delete" | "read";
  children: ReactNode;
  fallback?: ReactNode; // optional fallback UI
}

export function CanAccess({ resource, privilege, children, fallback = null }: CanAccessProps) {
  const access = useAccessContext();
  const allowed = hasAccess(access.permissions, resource, privilege);

  if (!allowed) return <>{fallback}</>;

  return <>{children}</>;
}

// // ✅ Example usage
// const DeleteUserButton = () => <button>Delete User</button>;

// export default function App() {
//   // Simulated API response
//   const permissions: Permissions = {
//     resource: {
//       tenant: ["all"],
//       user: ["create", "read"], // no "delete"
//     },
//   };

//   return (
//     <AccessContext.Provider value={{ permissions }}>
//       <h1>Dashboard</h1>

//       <CanAccess resource="user" action="create">
//         <button>Create User</button>
//       </CanAccess>

//       <CanAccess resource="user" action="delete" fallback={<span>No delete permission</span>}>
//         <DeleteUserButton />
//       </CanAccess>
//     </AccessContext.Provider>
//   );
// }
