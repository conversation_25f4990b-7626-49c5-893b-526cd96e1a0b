"use client";

import React, { ComponentType } from "react";
import { useAccessContext, hasAccess } from "@/components/access-provider";

// HOC
export function withAccessControl<P extends {}>(
  WrappedComponent: ComponentType<P>,
  resource: string,
  action: string
) {
  return function WithAccessControl(props: P) {
    const access = useAccessContext();
    const allowed = hasAccess(access.permissions, resource, action);

    if (!allowed) {
      return null; // Or replace with <span>No Access</span>
    }

    return <WrappedComponent {...props} />;
  };
}

// ✅ Example usage
// const CreateUserButton = () => <button>Create User</button>;

// const ProtectedCreateUserButton = withAccessControl(CreateUserButton, "user", "create");

// export default function App() {
//   // Simulated API response
//   const permissions: Permissions = {
//     resource: {
//       tenant: ["all", "create", "update", "delete", "read"],
//       user: ["all", "create", "update", "delete", "read"],
//     },
//   };

//   return (
//     <AccessContext.Provider value={{ permissions }}>
//       <div>
//         <h1>Dashboard</h1>
//         <ProtectedCreateUserButton />
//       </div>
//     </AccessContext.Provider>
//   );
// }
