"use client";

import React from "react";
import { Dialog, DialogContent } from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { useActivateTenant, useDeactivateTenant } from "@/api-slice/tenant/index";

export type Tenant = {
  id: string;
  tenantId: string;
  companyName: string;
  adminName: string;
  emailId: string;
  subscriptionType: string;
  contactNo: string;
  capOnUsers: number;
  address: string;
  createdAt: string;
  updatedAt: string;
  first_time_user: boolean;
  isActive: boolean;
};

interface ToggleTenantStatusDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedTenant: Tenant | null;
}

const ToggleTenantStatusDialog: React.FC<ToggleTenantStatusDialogProps> = ({
  open,
  onOpenChange,
  selectedTenant,
}) => {
  const activateTenant = useActivateTenant();
  const deactivateTenant = useDeactivateTenant();

  const handleConfirm = () => {
    if (!selectedTenant) return;

    if (selectedTenant.isActive) {
      deactivateTenant.mutate(
        { tenantId: selectedTenant.id, data: { isActive: false } },
        { onSuccess: () => onOpenChange(false) }
      );
    } else {
      activateTenant.mutate(
        { tenantId: selectedTenant.id, data: { isActive: true } },
        { onSuccess: () => onOpenChange(false) }
      );
    }
  };

  const isLoading = activateTenant.isPending || deactivateTenant.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <h2 className="text-center text-lg font-semibold">
          {selectedTenant?.isActive ? "Deactivate Tenant" : "Activate Tenant"}
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Notification will be sent to the registered tenant admin email.
        </p>
        <div className="flex justify-between gap-2">
          <Input value={selectedTenant?.emailId || ""} disabled />

          <Button onClick={handleConfirm} disabled={isLoading}>
            {isLoading ? "Confirming..." : "Confirm"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ToggleTenantStatusDialog;
