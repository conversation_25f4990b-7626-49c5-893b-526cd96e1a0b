"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";
import { useEditTenant } from "@/api-slice/tenant";
import { toast } from "sonner";

export type Tenant = {
  id: string;
  tenantId: string;
  companyName: string;
  adminName: string;
  emailId: string;
  subscriptionType: string;
  contactNo: string;
  capOnUsers: number;
  address: string;
  createdAt: string;
  updatedAt: string;
  first_time_user: boolean;
  users: Array<{
    id: string;
    firstName: string;
    lastName: string;
    displayName: string;
    emailId: string;
    role: string;
    isActive: boolean;
    tenantId: string;
    createdAt: string;
    updatedAt: string;
    lastLoginAt: any;
    first_time_user: boolean;
  }>;
};

type UpdateTenantPayload = {
  companyName: string;
  adminName: string;
  emailId: string;
  subscriptionType: string;
  contactNo: string;
  capOnUsers: number;
  address: string;
};

interface EditTenantFormProps {
  selectedTenant: Tenant;
  onClose: () => void;
}

const EditTenantForm: React.FC<EditTenantFormProps> = ({ selectedTenant, onClose }) => {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<UpdateTenantPayload>({
    defaultValues: {
      companyName: selectedTenant.companyName,
      adminName: selectedTenant.adminName,
      emailId: selectedTenant.emailId,
      subscriptionType: selectedTenant.subscriptionType,
      contactNo: selectedTenant.contactNo,
      capOnUsers: selectedTenant.capOnUsers || 1,
      address: selectedTenant.address,
    },
  });

  const userCapacity = watch("capOnUsers");
  const updateTenant = useEditTenant();
  const { isPending } = updateTenant;

  const onSubmit = (data: UpdateTenantPayload) => {
    updateTenant.mutate(
      { tenantId: selectedTenant.tenantId, data },
      {
        onSuccess: () => {
          onClose();
          toast.success("Tenant updated successfully");
        },
        onError: () => {
          toast.error("Something went wrong!");
        },
      }
    );
  };

  // Increment / Decrement User Capacity
  const increment = () => setValue("capOnUsers", (userCapacity || 0) + 1);
  const decrement = () => setValue("capOnUsers", Math.max(1, (userCapacity || 1) - 1));

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 p-6">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Company Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Company Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("companyName", { required: "Company name is required" })}
            placeholder="Company Name"
            className="h-10"
          />
          {errors.companyName && (
            <span className="text-xs text-red-500">{errors.companyName.message}</span>
          )}
        </div>

        {/* Admin Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Admin Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("adminName", { required: "Admin name is required" })}
            placeholder="Admin Name"
            className="h-10"
          />
          {errors.adminName && (
            <span className="text-xs text-red-500">{errors.adminName.message}</span>
          )}
        </div>

        {/* Admin Email */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Email <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("emailId", {
              required: "Admin email is required",
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: "Invalid email format",
              },
            })}
            placeholder="Admin Email"
            className="h-10"
            disabled
          />
          {errors.emailId && <span className="text-xs text-red-500">{errors.emailId.message}</span>}
        </div>

        {/* Subscription Type - Dropdown */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Subscription Type <span className="ml-1 text-red-500">*</span>
          </label>
          <select
            {...register("subscriptionType", { required: "Subscription type is required" })}
            className="border-input bg-background h-10 rounded-md border px-3 text-sm"
          >
            <option value="">Select Subscription</option>
            <option value="basic">Basic</option>
            <option value="standard">Standard</option>
            <option value="premium">Premium</option>
          </select>
          {errors.subscriptionType && (
            <span className="text-xs text-red-500">{errors.subscriptionType.message}</span>
          )}
        </div>

        {/* Contact Number */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Contact No <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("contactNo", {
              required: "Contact number is required",
              pattern: {
                value: /^[0-9]{10}$/,
                message: "Contact number must be 10 digits",
              },
            })}
            placeholder="Contact Number"
            className="h-10"
          />
          {errors.contactNo && (
            <span className="text-xs text-red-500">{errors.contactNo.message}</span>
          )}
        </div>

        {/* Address */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Address <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("address", { required: "Address is required" })}
            placeholder="Address"
            className="h-10"
          />
          {errors.address && <span className="text-xs text-red-500">{errors.address.message}</span>}
        </div>

        {/* User Capacity with + / - */}
        <div className="flex flex-col space-y-1 md:col-span-2">
          <div className="flex items-center justify-between">
            <p>User Capacity</p>
            <div className="flex items-center gap-2">
              <Button type="button" variant="outline" onClick={decrement}>
                -
              </Button>
              <Input
                type="number"
                {...register("capOnUsers", {
                  required: "User capacity is required",
                  min: { value: 1, message: "User capacity must be at least 1" },
                })}
                value={userCapacity}
                readOnly
                className="h-10 w-20 text-center"
              />
              <Button type="button" variant="outline" onClick={increment}>
                +
              </Button>
            </div>
          </div>
          {errors.capOnUsers && (
            <span className="text-xs text-red-500">{errors.capOnUsers.message}</span>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between">
        <Button
          type="button"
          variant="secondary"
          className="w-full px-8 md:w-auto"
          onClick={onClose}
        >
          Cancel
        </Button>
        <Button type="submit" className="w-full px-8 md:w-auto">
          {isPending ? "Saving..." : "Save"}
        </Button>
      </div>
    </form>
  );
};

export default EditTenantForm;
