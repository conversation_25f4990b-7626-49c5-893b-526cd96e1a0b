"use client";

import React, { useState } from "react";
import { getCoreRowModel, type ColumnDef } from "@tanstack/react-table";

import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { TableColumnHeader } from "./lib/table-config";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";

import EditTenantDialog from "./edit-tenant-dialog";
import ToggleTenantStatusDialog from "./toggle-tenant-status-diaglog";
import { CustomVirtualList } from "@/components/custom-virtual-list";

export type Tenant = {
  id: string;
  tenantId: string;
  companyName: string;
  adminName: string;
  emailId: string;
  subscriptionType: string;
  contactNo: string;
  capOnUsers: number;
  address: string;
  createdAt: string;
  updatedAt: string;
  first_time_user: boolean;
  isActive: boolean;
  users: Array<{
    id: string;
    firstName: string;
    lastName: string;
    displayName: string;
    emailId: string;
    role: string;
    isActive: boolean;
    tenantId: string;
    createdAt: string;
    updatedAt: string;
    lastLoginAt: any;
    first_time_user: boolean;
  }>;
};

interface TenantTableProps {
  tenantData: Tenant[];
}

const TenantTable: React.FC<TenantTableProps> = ({ tenantData }) => {
  const [openEdit, setOpenEdit] = useState(false);
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);

  const [openToggle, setOpenToggle] = useState(false);

  const columns: ColumnDef<Tenant>[] = [
    {
      accessorKey: "companyName",
      header: ({ column }) => <TableColumnHeader column={column} title="Company Name" />,
      cell: ({ row }) => <div className="font-medium">{row.getValue("companyName")}</div>,
      enableSorting: false,
      size: 200,
    },
    {
      accessorKey: "tenantId",
      header: ({ column }) => <TableColumnHeader column={column} title="Tenant ID" />,
      cell: ({ row }) => <div className="font-medium">{row.getValue("tenantId")}</div>,
      enableSorting: false,
      size: 200,
    },
    {
      accessorKey: "adminName",
      header: ({ column }) => <TableColumnHeader column={column} title="Admin Name" />,
      cell: ({ row }) => <div className="text-muted-foreground">{row.getValue("adminName")}</div>,
      enableSorting: false,
      size: 200,
    },
    {
      accessorKey: "subscriptionType",
      header: ({ column }) => <TableColumnHeader column={column} title="Subscription Type" />,
      cell: ({ row }) => (
        <div className="text-muted-foreground">{row.getValue("subscriptionType")}</div>
      ),
      enableSorting: false,
      size: 200,
    },
    {
      accessorKey: "emailId",
      header: ({ column }) => <TableColumnHeader column={column} title="Email" />,
      cell: ({ row }) => <div className="font-medium">{row.getValue("emailId")}</div>,
      enableSorting: false,
      size: 300,
    },
    {
      accessorKey: "contactNo",
      header: ({ column }) => <TableColumnHeader column={column} title="Contact" />,
      cell: ({ row }) => <div className="font-medium">{row.getValue("contactNo")}</div>,
      enableSorting: false,
      size: 200,
    },
    {
      id: "actions",
      header: "Action",
      cell: ({ row }) => {
        const tenant = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <Icons.moreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => {
                  setSelectedTenant(tenant);
                  setOpenEdit(true);
                }}
              >
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  setSelectedTenant(tenant);
                  setOpenToggle(true);
                }}
              >
                {tenant.isActive ? "Deactivate Tenant" : "Activate Tenant"}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="bg-background rounded-sm p-6">
      <div className="mx-auto">
        {/* Header */}
        <div className="mb-8 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" className="p-2">
              <Icons.arrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-foreground text-2xl font-semibold">Tenant Management</h1>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="gap-2 bg-transparent">
              <Icons.download className="h-4 w-4" />
              Export
            </Button>
            <Button variant="outline" size="sm" className="gap-2 bg-transparent">
              <Icons.sortAsc className="h-4 w-4" />
              Sort
            </Button>
            <Button variant="outline" size="sm" className="gap-2 bg-transparent">
              <Icons.filter className="h-4 w-4" />
              Filter
            </Button>
          </div>
        </div>

        {/* Table */}
        <CustomVirtualList
          options={{
            data: tenantData,
            columns,
            getCoreRowModel: getCoreRowModel(),
          }}
        />
      </div>

      {/* Edit Tenant Dialog */}
      <EditTenantDialog
        open={openEdit}
        onOpenChange={setOpenEdit}
        selectedTenant={selectedTenant}
      />

      {/* Toggle Tenant Status Dialog */}
      <ToggleTenantStatusDialog
        open={openToggle}
        onOpenChange={setOpenToggle}
        selectedTenant={selectedTenant}
      />
    </div>
  );
};

export default TenantTable;
