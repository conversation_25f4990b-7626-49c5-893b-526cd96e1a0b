"use client";
import React, { useState } from "react";
import { SearchBar } from "@/components/search-bar";
import TenantTable from "../tenant-table";
import { SubNavBar } from "@/components/sub-nav-bar";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@repo/ui/components/dialog";
import AddTenantForm from "../add-tenant-form";
import { useGetTenants } from "@/api-slice/tenant";
import Loading from "@repo/ui/components/loading";

const TenantListView: React.FC = () => {
  const [open, setOpen] = useState(false);
  const { data: tenantData, isLoading, error } = useGetTenants();

  if (isLoading) return <Loading />;
  if (error) return <div>Something went wrong...</div>;

  return (
    <div className="flex h-full flex-col">
      <SearchBar />

      <div className="py-2">
        <SubNavBar actionButton={[{ name: "Add Tenant", onClick: () => setOpen(true) }]} />
      </div>

      <TenantTable tenantData={tenantData ?? []} />

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle className="text-center">User Registry</DialogTitle>
          </DialogHeader>
          <AddTenantForm onSuccess={() => setOpen(false)} />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TenantListView;
