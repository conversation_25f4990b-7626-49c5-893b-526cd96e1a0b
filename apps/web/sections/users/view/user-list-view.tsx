"use client";
import React, { useCallback, useState } from "react";
import { SearchBar } from "@/components/search-bar";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@repo/ui/components/dialog";
import { SubNavBar } from "@/components/sub-nav-bar";
import UsersTable from "../user-table";
import { useGetUsersList } from "@/api-slice/users";
import AddUserForm from "../add-user-form";
import Loading from "@repo/ui/components/loading";
import { ErrorBoundary } from "@/components/error-boundary";

const UserList: React.FC = () => {
  const [openAdd, setOpenAdd] = useState(false);

  // const { data: userData, isLoading, error } = useGetUsers();

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading, error, refetch } =
    useGetUsersList(10);

  // Flatten pages into one list
  const users = data?.pages.flatMap((page) => page.users) ?? [];

  // Scroll handler (when near bottom, fetch next page)
  const handleScrollEnd = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  if (isLoading) return <Loading />;

  if (error) return <ErrorBoundary error={error} onRetry={() => refetch} />;

  return (
    <div className="flex h-full flex-col">
      <SearchBar />

      {/* Top Bar */}
      <div className="py-2">
        <SubNavBar actionButton={[{ name: "Add User", onClick: () => setOpenAdd(true) }]} />
      </div>

      {/* Users Table */}
      <UsersTable userData={users || []} onEndReached={handleScrollEnd} />

      {/* Add User */}
      <Dialog open={openAdd} onOpenChange={setOpenAdd}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle className="text-center">User Registry</DialogTitle>
          </DialogHeader>
          <AddUserForm onSuccess={() => setOpenAdd(false)} />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UserList;
