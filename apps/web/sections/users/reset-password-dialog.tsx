"use client";

import React from "react";
import { Dialog, DialogContent } from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { useResetPasswordUser } from "@/api-slice/users";
import { toast } from "sonner";
import EmailPassword from "supertokens-auth-react/recipe/emailpassword";

type User = {
  id: string;
  firstName: string;
  lastName: string;
  displayName: string;
  emailId: string;
  role: string;
  isActive: boolean;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt: any;
  first_time_user: boolean;
  tenant: {
    id: string;
    tenantId: string;
    companyName: string;
    adminName: string;
    emailId: string;
    subscriptionType: string;
    contactNo: string;
    capOnUsers: number;
    address: string;
    createdAt: string;
    updatedAt: string;
    first_time_user: boolean;
  };
};

interface ResetPasswordUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedUser: User | null;
}

const ResetPasswordUserDialog: React.FC<ResetPasswordUserDialogProps> = ({
  open,
  onOpenChange,
  selectedUser,
}) => {
  const resetPassword = useResetPasswordUser();
  const { isPending } = resetPassword;

  const handleConfirm = async () => {
    if (!selectedUser) return;

    try {
      const response = await EmailPassword.sendPasswordResetEmail({
        formFields: [{ id: "email", value: selectedUser.emailId }],
      });

      switch (response.status) {
        case "OK": {
          onOpenChange(false);
          toast.success("Password reset email sent! Check your inbox.");
          break;
        }

        case "PASSWORD_RESET_NOT_ALLOWED": {
          toast.error("Please verify your email before resetting your password.");
          break;
        }

        default: {
          onOpenChange(false);
          toast.error("Something went wrong. Please try again.");
        }
      }
    } catch (err) {
      onOpenChange(false);
      toast.error("Something went wrong. Please try again.");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <h2 className="text-center text-lg font-semibold">Reset Password</h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Reset password link will be sent to the below email address.
        </p>
        <div className="flex justify-between gap-2">
          <Input value={selectedUser?.emailId || ""} disabled />

          <Button onClick={handleConfirm} disabled={isPending}>
            {isPending ? "Confirming..." : "Confirm"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ResetPasswordUserDialog;
