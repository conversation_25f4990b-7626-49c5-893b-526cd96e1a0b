"use client";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";
import { useCreateUser } from "@/api-slice/users";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@repo/ui/components/select";
import { toast } from "sonner";

type CreateUserPayload = {
  firstName: string;
  lastName: string;
  displayName?: string;
  emailId: string;
  role: string;
};

type AddUserFormProps = {
  onSuccess?: () => void;
};

const AddUserForm: React.FC<AddUserFormProps> = ({ onSuccess }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<CreateUserPayload>();

  const createUser = useCreateUser();
  const { isPending } = createUser;

  // Watch firstName and lastName
  const firstName = watch("firstName");
  const lastName = watch("lastName");
  const displayName = watch("displayName");

  // Update displayName automatically if user hasn't typed manually
  useEffect(() => {
    if (!displayName || displayName === `${firstName || ""} ${lastName || ""}`.trim()) {
      setValue("displayName", `${firstName || ""} ${lastName || ""}`.trim(), {
        shouldValidate: true,
      });
    }
  }, [firstName, lastName]);

  const onSubmit = (data: CreateUserPayload) => {
    createUser.mutate(data, {
      onSuccess: () => {
        onSuccess?.();
        toast.success("User created successfully");
      },
      onError: () => {
        toast.error("Something went wrong!");
      },
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="max-w-4xl space-y-6 p-6">
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {/* First Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            First Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("firstName", { required: "First name is required" })}
            placeholder="First Name"
            className="h-10"
          />
          {errors.firstName && (
            <span className="text-xs text-red-500">{errors.firstName.message}</span>
          )}
        </div>

        {/* Last Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Last Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("lastName", { required: "Last name is required" })}
            placeholder="Last Name"
            className="h-10"
          />
          {errors.lastName && (
            <span className="text-xs text-red-500">{errors.lastName.message}</span>
          )}
        </div>

        {/* Display Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">Display Name</label>
          <Input {...register("displayName")} placeholder="Display Name" className="h-10" />
        </div>

        {/* Email ID */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Email ID <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("emailId", {
              required: "Email is required",
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: "Invalid email format",
              },
            })}
            placeholder="Email ID"
            className="h-10"
          />
          {errors.emailId && <span className="text-xs text-red-500">{errors.emailId.message}</span>}
        </div>

        {/* User Role */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            User Role <span className="ml-1 text-red-500">*</span>
          </label>
          <Select
            onValueChange={(val) => setValue("role", val, { shouldValidate: true })}
            value={watch("role") || ""}
          >
            <SelectTrigger className="h-10">
              <SelectValue placeholder="Select User Role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="admin">Admin</SelectItem>
              <SelectItem value="user">User</SelectItem>
            </SelectContent>
          </Select>
          {errors.role && <span className="text-xs text-red-500">{errors.role.message}</span>}
        </div>
      </div>

      <Button type="submit" className="w-full px-8 md:w-auto">
        {isPending ? "Registering..." : "Register User"}
      </Button>
    </form>
  );
};

export default AddUserForm;
