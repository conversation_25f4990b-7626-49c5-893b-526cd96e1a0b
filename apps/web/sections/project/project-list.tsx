"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { getCoreRowModel } from "@tanstack/react-table";

import { But<PERSON> } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { TableColumnHeader } from "./project-table";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";

import TenantVirtualList from "./project-virtual-list";
import SelectedProjectCard from "./selected-project-card";

import Image from "next/image";

import imageSrc from "../../public/ship.png";

type Vessel = {
  id: string;
  vesselName: string;
  code: string;
  company: string;
  vesselType: string;
  totalFiles: number;
  status: string;
};

const data: Vessel[] = [
  {
    id: "1",
    vesselName: "Atlas",
    code: "AT001",
    company: "XY Shipping",
    vesselType: "Bulk Carrier",
    totalFiles: 310,
    status: "Processing",
  },
  {
    id: "2",
    vesselName: "Coral Sea",
    code: "ST002",
    company: "XY Shipping",
    vesselType: "Container",
    totalFiles: 240,
    status: "Processing",
  },
  {
    id: "3",
    vesselName: "Pacific Fortune",
    code: "PF003",
    company: "XY Shipping",
    vesselType: "Tanker",
    totalFiles: 440,
    status: "Processing",
  },
  {
    id: "4",
    vesselName: "Endurance",
    code: "ET004",
    company: "XY Shipping",
    vesselType: "Bulk Carrier",
    totalFiles: 340,
    status: "Processing",
  },
  {
    id: "5",
    vesselName: "Star Vista",
    code: "ST005",
    company: "XY Shipping",
    vesselType: "Bulk Carrier",
    totalFiles: 340,
    status: "Processing",
  },
];

const columns: ColumnDef<Vessel>[] = [
  {
    accessorKey: "vesselName",
    header: ({ column }) => <TableColumnHeader column={column} title="Vessel Name" />,
    cell: ({ row }) => {
      const vesselName = row.getValue("vesselName") as string;
      // const vesselImage = row.original.vesselImage; // <-- Make sure your data has this field

      return (
        <div className="flex items-center gap-3">
          <Image
            src={imageSrc || "/placeholder.png"} // fallback image if not available
            alt={vesselName}
            width={50}
            height={50}
            className="rounded-md object-cover"
          />
          <span className="text-gray-800">{vesselName}</span>
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: "vesselType",
    header: ({ column }) => <TableColumnHeader column={column} title="Vessel Type" />,
    cell: ({ row }) => row.getValue("vesselType"),
    enableSorting: false,
  },
  {
    accessorKey: "totalFiles",
    header: ({ column }) => <TableColumnHeader column={column} title="Total Files" />,
    cell: ({ row }) => row.getValue("totalFiles"),
    enableSorting: false,
  },
  {
    accessorKey: "status",
    header: ({ column }) => <TableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => row.getValue("status"),
    enableSorting: false,
  },
  {
    id: "actions",
    header: "Action",
    cell: ({ row }) => {
      return (
        <div className="flex items-center justify-between gap-2">
          <Button>Edit</Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <Icons.moreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>Activate</DropdownMenuItem>
              <DropdownMenuItem className="text-destructive">Deactivate</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
  },
];

export default function ProjectTable() {
  return (
    <div className="grid w-full grid-cols-1 gap-6 lg:grid-cols-[1fr_400px]">
      {/* Table Section */}
      <div className="bg-background rounded-lg p-6 shadow-sm">
        <div className="bg-card overflow-hidden rounded-lg border-gray-200">
          <TenantVirtualList
            options={{
              data: data,
              columns,
              getCoreRowModel: getCoreRowModel(), // required
            }}
          />
        </div>
      </div>

      {/* Project Details Section */}
      <SelectedProjectCard />
    </div>
  );
}
