[{"name": "Project Details", "form": [{"name": "Project Code", "key": "projectCode", "placeholder": "Enter Project Code", "validation": {"required": true, "message": "Project Code is required"}}, {"name": "Client", "key": "client", "placeholder": "Enter Client Name", "validation": {"required": true, "message": "Client is required"}}, {"name": "<PERSON><PERSON><PERSON>", "key": "vessel", "placeholder": "<PERSON><PERSON>", "validation": {"required": true, "message": "Vessel is required"}}, {"name": "Category", "key": "category", "placeholder": "Enter Category", "validation": {"required": true, "message": "Category is required"}}, {"name": "Status", "key": "status", "placeholder": "Enter Status", "validation": {"required": true, "message": "Status is required"}}, {"name": "Tags", "key": "tags", "placeholder": "Enter Tags", "validation": {"required": true, "message": "Tags are required"}}]}, {"name": "<PERSON><PERSON><PERSON>", "form": [{"name": "Shipyard", "key": "shipyard", "placeholder": "Enter Shipyard", "validation": {"required": true, "message": "Shipyard is required"}}, {"name": "Hull Number", "key": "hullNumber", "placeholder": "Enter Hull Number", "validation": {"required": true, "message": "Hull Number is required"}}, {"name": "IMO Number", "key": "IMONumber", "placeholder": "Enter IMO Number", "validation": {"required": true, "message": "IMO Number is required"}}, {"name": "Vessel Type", "key": "vesselType", "placeholder": "Enter Vessel Type", "validation": {"required": true, "message": "Vessel Type is required"}}, {"name": "Vessel Delivery Date", "key": "vesselDeliveryDate", "placeholder": "Select Delivery Date", "type": "date", "validation": {"required": true, "message": "Delivery Date is required"}}]}]