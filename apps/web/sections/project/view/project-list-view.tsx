"use client";
import React, { useState } from "react";
import { SearchBar } from "@/components/search-bar";
import { Dialog, DialogContent } from "@repo/ui/components/dialog";
import { SubNavBar } from "@/components/sub-nav-bar";
import TenantTable from "../project-list";
import AddPeojectFormView from "./add-project-form-view";

const ProjectListView: React.FC = () => {
  const [open, setOpen] = useState(false);

  return (
    <div className="h-fit w-full bg-[#f0f7f6] p-10">
      <SearchBar />

      <Dialog open={open} onOpenChange={setOpen}>
        <div className="py-3">
          <SubNavBar
            actionButton={[
              { name: "Sort", onClick: () => setOpen(true) },
              { name: "Filter", onClick: () => setOpen(true) },
              { name: "New Project", onClick: () => setOpen(true) },
            ]}
          />
        </div>

        <TenantTable />

        <DialogContent className="sm:max-w-[800px]">
          <AddPeojectFormView />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProjectListView;
