import React from "react";
import dayjs from "dayjs";
import { Progress } from "@repo/ui/components/progress";
import { Button } from "@repo/ui/components/button";

const SelectedProjectCard = () => {
  return (
    <div className="w-full rounded-2xl border border-gray-200 bg-white shadow-md">
      {/* Content */}
      <div className="space-y-5 p-5">
        {/* Banner */}
        <div className="mt-2 h-[47px] w-full rounded-md bg-[#C7ECEB]" />

        {/* Project Header */}
        <div>
          <p className="text-sm text-gray-500">
            ZX Shipping Project Code : <span className="font-medium">ZX-001</span>
          </p>
          <h1 className="mt-1 text-xl font-semibold text-gray-800">MV Atlas</h1>
        </div>

        <hr className="border-gray-200" />

        {/* Project Meta Info */}
        <div className="space-y-1">
          <h2 className="text-lg font-semibold text-gray-700">Project Meta Info</h2>
          <p className="text-sm text-gray-600">
            Start Date: {dayjs(new Date()).format("DD/MM/YYYY")}
          </p>
          <p className="text-sm text-gray-600">
            Created By: <span className="font-medium">Sanjay M</span>
          </p>
          <p className="text-sm text-gray-600">
            Last Modified By: <span className="font-medium">Sanjay M</span>,{" "}
            {dayjs(new Date()).format("lll")}
          </p>
        </div>

        <hr className="border-gray-200" />

        {/* Status & Progress */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold text-gray-700">Status & Progress</h2>
          <p className="text-sm text-gray-600">
            Status : <span className="font-medium text-green-500">Processing</span>
          </p>
          <Progress value={33} />
        </div>

        <hr className="border-gray-200" />

        {/* File Breakdown */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold text-gray-700">File Breakdown</h2>
          <div className="inline-block space-y-2 text-sm text-gray-600">
            <p>
              Total Files: <span className="font-medium">123</span>
            </p>

            <p className="rounded-sm bg-[#E6F0FA] px-2 py-1">
              Digitised: <span className="font-medium">43</span>
            </p>

            <p className="rounded-sm bg-[#F2E6FA] px-2 py-1">
              Marked: <span className="font-medium">43</span>
            </p>

            <p className="rounded-sm bg-[#E6F4ED] px-2 py-1">
              Extracted: <span className="font-medium">43</span>
            </p>
          </div>

          <Button className="mt-3 w-full">View Project Detail</Button>
        </div>
      </div>
    </div>
  );
};

export default SelectedProjectCard;
