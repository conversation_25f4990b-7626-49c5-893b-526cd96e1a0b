import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import React from "react";
import { useForm } from "react-hook-form";
import addProjectFromConfig from "./lib/add-project-form-config.json";

const AddProjectForm: React.FC = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const onSubmit = (data: Record<string, any>) => {
    console.log("Form Data:", data);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
      {addProjectFromConfig.map((section, index) => (
        <div key={index} className="space-y-4 p-2">
          {/* Section Title */}
          <h2 className="inline-block rounded-sm bg-[#C7ECEB] p-2 text-lg font-medium">
            {section.name}
          </h2>

          {/* Section Fields in Grid */}
          <div className="grid grid-cols-3 gap-4">
            {section.form.map((field) => (
              <div key={field.key} className="flex flex-col space-y-1">
                <label className="text-sm font-medium">{field.name}</label>
                <Input
                  {...register(field.key, field.validation)}
                  placeholder={field.placeholder}
                  className="h-10"
                />
                {errors[field.key] && (
                  <span className="text-xs text-red-500">
                    {errors[field.key]?.message as string}
                  </span>
                )}
              </div>
            ))}
          </div>
        </div>
      ))}

      <Button type="submit" className="w-full">
        Submit
      </Button>
    </form>
  );
};

export default AddProjectForm;
