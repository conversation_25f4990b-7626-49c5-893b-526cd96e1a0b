import React from "react";
import { useReactTable, TableOptions, flexRender } from "@tanstack/react-table";

interface CustomVirtualListProps<TData extends object> {
  options: TableOptions<TData>;
}

const TenantVirtualList = <TData extends object>({ options }: CustomVirtualListProps<TData>) => {
  const table = useReactTable(options);

  const headers = table.getHeaderGroups();
  const rows = table.getRowModel().rows;

  return (
    <div className="w-full">
      {/* Headers */}
      <div className="flex w-full text-left font-medium">
        {headers.map((headerGroup) => (
          <div key={headerGroup.id} className="flex w-full flex-1">
            {headerGroup.headers.map((header) => (
              <div key={header.id} className="flex-1 p-2 text-left">
                {header.isPlaceholder
                  ? null
                  : flexRender(header.column.columnDef.header, header.getContext())}
              </div>
            ))}
          </div>
        ))}
      </div>

      {/* Rows */}
      <div className="h-fit w-full overflow-x-scroll text-left">
        {rows.map((row) => (
          <div
            key={row.id}
            className="my-4 flex w-full items-center rounded-md border-none bg-[#f0f7f6]"
          >
            {row.getVisibleCells().map((cell) => (
              <div key={cell.id} className="flex-1 p-2">
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

export default TenantVirtualList;
