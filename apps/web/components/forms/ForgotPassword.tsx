"use client";
import * as React from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader } from "@repo/ui/components/card";
import { Icons } from "@repo/ui/components/icons";
import { Input } from "@repo/ui/components/input";
import EmailPassword from "supertokens-auth-react/recipe/emailpassword";
import { useRouter } from "next/navigation";
import logo from "@/public/logo.png";
import Image from "next/image";

export interface ForgotPasswordFormProps {}

const schema = z.object({
  email: z.string().email("Enter a valid email address"),
});

type ForgotPasswordFormValues = z.infer<typeof schema>;

const ForgotPasswordForm = React.forwardRef<HTMLDivElement, ForgotPasswordFormProps>(({}, ref) => {
  const [error, setError] = React.useState<string | null>(null);
  const [success, setSuccess] = React.useState<string | null>(null);

  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError: setFormError,
  } = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(schema),
  });

  const onSubmit = async (data: ForgotPasswordFormValues) => {
    setError(null);
    setSuccess(null);

    try {
      const response = await EmailPassword.sendPasswordResetEmail({
        formFields: [{ id: "email", value: data.email }],
      });

      switch (response.status) {
        case "FIELD_ERROR": {
          const fieldError = response?.formFields[0];
          if (fieldError) {
            setFormError(fieldError.id as keyof ForgotPasswordFormValues, {
              type: "server",
              message: fieldError.error,
            });
          }
          break;
        }

        case "OK": {
          setSuccess("Password reset email sent! Check your inbox.");
          break;
        }

        case "PASSWORD_RESET_NOT_ALLOWED": {
          setError("Please verify your email before resetting your password.");
          break;
        }

        default: {
          setError("Something went wrong. Please try again.");
        }
      }
    } catch (err) {
      setError("Something went wrong. Please try again.");
    }
  };

  const handleBackToLogin = () => {
    router.push("/login");
  };

  return (
    <div ref={ref} className="w-full max-w-lg">
      <Card className="w-full border-0 shadow-none">
        <CardHeader className="text-left">
          <div className="mb-4 flex flex-col items-start">
            <Image src={logo} alt="Cadet Labs" width={40} height={40} className="mr-2" />
          </div>
          <div>
            <h1 className="text-primary mb-0 text-3xl font-bold leading-snug">PMS Asset Builder</h1>
            <h1 className="text-primary text-2xl leading-snug">Forgot Password?</h1>
            <p className="mt-2 text-xs text-gray-500">
              No worries, we'll send you reset instructions
            </p>
          </div>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="relative">
              <Input
                id="email"
                type="email"
                placeholder="Email Address"
                disabled={isSubmitting}
                icon={<Icons.mail className="h-4 w-4" />}
                iconPosition="left"
                {...register("email")}
              />
            </div>
            {errors.email && <p className="text-sm text-red-500">{errors.email.message}</p>}

            {/* Success message */}
            {success && <p className="text-sm text-green-600">{success}</p>}

            {/* Error message */}
            {error && <p className="text-sm text-red-500">{error}</p>}

            <div>
              <Button type="submit" className="w-full rounded-lg" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                    Sending reset email...
                  </>
                ) : (
                  <>
                    Reset Password
                    <Icons.arrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>

              <div className="mt-2 text-left">
                <button
                  type="button"
                  onClick={handleBackToLogin}
                  className="cursor-pointer text-xs text-gray-500 underline hover:text-gray-700"
                  disabled={isSubmitting}
                >
                  Remember your password? Back to login
                </button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
});

ForgotPasswordForm.displayName = "ForgotPasswordForm";
export { ForgotPasswordForm };
