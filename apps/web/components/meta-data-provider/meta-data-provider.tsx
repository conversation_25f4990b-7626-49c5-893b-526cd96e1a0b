"use client";
import React, { createContext, ReactNode } from "react";
import { useGetMetaData } from "@/api-slice/common";
import Loading from "@repo/ui/components/loading";
import { ErrorBoundary } from "../error-boundary";

export interface MetaDataContextType {
  metaData: any | null;
}

interface MetaDataProviderProps {
  children: ReactNode;
}

export const MetaDataContext = createContext<MetaDataContextType | undefined>(undefined);

const MetaDataProvider: React.FC<MetaDataProviderProps> = ({ children }) => {
  const { data, isLoading, error, refetch } = useGetMetaData();

  if (isLoading) return <Loading />;

  if (error) {
    return <ErrorBoundary error={error} onRetry={() => refetch()} />;
  }

  return (
    <MetaDataContext.Provider value={{ metaData: data ?? null }}>
      {children}
    </MetaDataContext.Provider>
  );
};

export default MetaDataProvider;
