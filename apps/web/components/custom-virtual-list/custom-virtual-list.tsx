import React, { useEffect, useRef } from "react";
import { useReactTable, TableOptions, flexRender } from "@tanstack/react-table";

interface CustomVirtualListProps<TData extends object> {
  options: TableOptions<TData>;
  rowHeight?: number;
  rowGap?: number;
  onEndReached?: () => void;
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
}

const CustomVirtualList = <TData extends object>({
  options,
  rowHeight = 48,
  rowGap = 10,
  onEndReached,
  hasNextPage = false,
  isFetchingNextPage = false,
}: CustomVirtualListProps<TData>) => {
  const table = useReactTable(options);

  const containerRef = useRef<HTMLDivElement>(null);

  const headers = table.getHeaderGroups();
  const rows = table.getRowModel().rows;

  // detect scroll bottom
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !onEndReached) return;

    const handleScroll = () => {
      if (
        container.scrollTop + container.clientHeight >=
        container.scrollHeight - 50 // 50px threshold
      ) {
        onEndReached();
        console.log("Reached");
      }
    };

    // Check if content is not scrollable but there are more pages
    const checkIfScrollable = () => {
      if (hasNextPage && container.scrollHeight <= container.clientHeight) {
        // Content is not scrollable but there are more pages, trigger onEndReached
        onEndReached();
        console.log("Auto-triggered: Content not scrollable but has next page");
      }
    };

    container.addEventListener("scroll", handleScroll);

    // Check immediately after render
    const timeoutId = setTimeout(checkIfScrollable, 100);

    return () => {
      container.removeEventListener("scroll", handleScroll);
      clearTimeout(timeoutId);
    };
  }, [onEndReached, hasNextPage]);

  return (
    <div className="w-full">
      {/* Header */}
      <div className="sticky top-0 z-10 flex w-full bg-white text-left font-medium">
        {headers[0]?.headers.map((header) => (
          <div key={header.id} className="p-2 text-left" style={{ width: header.getSize() }}>
            {header.isPlaceholder
              ? null
              : flexRender(header.column.columnDef.header, header.getContext())}
          </div>
        ))}
      </div>

      {/* Rows */}
      <div ref={containerRef} className="relative h-[400px] overflow-y-auto">
        {rows.map((row, index) => (
          <div
            key={row.id}
            className="absolute left-0 flex items-center rounded-md bg-[#f0f7f6] shadow-sm"
            style={{
              top: index * (rowHeight + rowGap),
              height: rowHeight,
              width: "100%",
            }}
          >
            {row.getVisibleCells().map((cell) => (
              <div key={cell.id} className="truncate p-2" style={{ width: cell.column.getSize() }}>
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </div>
            ))}
          </div>
        ))}
        {/* Loading indicator when fetching next page */}
        {isFetchingNextPage && (
          <div
            className="absolute left-0 flex items-center justify-center rounded-md bg-gray-100"
            style={{
              top: rows.length * (rowHeight + rowGap),
              height: rowHeight,
              width: "100%",
            }}
          >
            <div className="text-sm text-gray-600">Loading more...</div>
          </div>
        )}

        {/* Spacer to ensure scrollable content when there are more pages */}
        <div
          style={{
            height: Math.max(
              rows.length * (rowHeight + rowGap) + (isFetchingNextPage ? rowHeight + rowGap : 0),
              hasNextPage ? 450 : rows.length * (rowHeight + rowGap) // Ensure 450px when there are more pages
            ),
          }}
        />
      </div>
    </div>
  );
};

export default CustomVirtualList;
