import React, { useEffect, useRef, useCallback } from "react";
import { useReactTable, TableOptions, flexRender } from "@tanstack/react-table";

interface CustomVirtualListProps<TData extends object> {
  options: TableOptions<TData>;
  rowHeight?: number;
  rowGap?: number;
  onEndReached?: () => void;
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
}

const CustomVirtualList = <TData extends object>({
  options,
  rowHeight = 48,
  rowGap = 10,
  onEndReached,
  hasNextPage = false,
  isFetchingNextPage = false,
}: CustomVirtualListProps<TData>) => {
  const table = useReactTable(options);

  const containerRef = useRef<HTMLDivElement>(null);

  const headers = table.getHeaderGroups();
  const rows = table.getRowModel().rows;

  // Auto-load pages when there's available space (debounced)
  const autoLoadIfSpaceAvailable = useCallback(() => {
    const container = containerRef.current;
    if (!container || !onEndReached || !hasNextPage || isFetchingNextPage) return;

    if (container.scrollHeight <= container.clientHeight) {
      console.log("Auto-loading next page - space available");
      onEndReached();
    }
  }, [onEndReached, hasNextPage, isFetchingNextPage]);

  // detect scroll bottom and auto-load when space is available
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !onEndReached) return;

    const handleScroll = () => {
      if (
        container.scrollTop + container.clientHeight >=
        container.scrollHeight - 50 // 50px threshold
      ) {
        onEndReached();
        console.log("Scroll reached bottom");
      }
    };

    container.addEventListener("scroll", handleScroll);

    // Check for auto-loading with a small delay to allow rendering
    const timeoutId = setTimeout(autoLoadIfSpaceAvailable, 100);

    return () => {
      container.removeEventListener("scroll", handleScroll);
      clearTimeout(timeoutId);
    };
  }, [onEndReached, autoLoadIfSpaceAvailable, rows.length]); // Re-run when data changes

  return (
    <div className="w-full">
      {/* Header */}
      <div className="sticky top-0 z-10 flex w-full bg-white text-left font-medium">
        {headers[0]?.headers.map((header) => (
          <div key={header.id} className="p-2 text-left" style={{ width: header.getSize() }}>
            {header.isPlaceholder
              ? null
              : flexRender(header.column.columnDef.header, header.getContext())}
          </div>
        ))}
      </div>

      {/* Rows */}
      <div ref={containerRef} className="relative h-[400px] overflow-y-auto">
        {rows.map((row, index) => (
          <div
            key={row.id}
            className="absolute left-0 flex items-center rounded-md bg-[#f0f7f6] shadow-sm"
            style={{
              top: index * (rowHeight + rowGap),
              height: rowHeight,
              width: "100%",
            }}
          >
            {row.getVisibleCells().map((cell) => (
              <div key={cell.id} className="truncate p-2" style={{ width: cell.column.getSize() }}>
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </div>
            ))}
          </div>
        ))}
        {/* Loading indicator when fetching next page */}
        {isFetchingNextPage && (
          <div
            className="absolute left-0 flex items-center justify-center rounded-md bg-gray-100"
            style={{
              top: rows.length * (rowHeight + rowGap),
              height: rowHeight,
              width: "100%",
            }}
          >
            <div className="text-sm text-gray-600">Loading more...</div>
          </div>
        )}

        {/* Spacer for proper virtual list height */}
        <div
          style={{
            height:
              rows.length * (rowHeight + rowGap) + (isFetchingNextPage ? rowHeight + rowGap : 0),
          }}
        />
      </div>
    </div>
  );
};

export default CustomVirtualList;
