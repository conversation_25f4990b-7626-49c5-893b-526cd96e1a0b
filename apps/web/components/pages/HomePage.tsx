"use client";

import * as React from "react";
import shipImg from "@/public/ship.png";
import Image from "next/image";

export default function HomePage({ FormComponent }: { FormComponent: React.ReactNode }) {
  const handleLogin = (data: { email: string; password: string }) => {
    console.log("Login attempt:", data);
    // TODO: integrate API call
  };

  const handleForgotPassword = () => {
    console.log("Forgot password clicked");
    // TODO: show forgot password flow
  };

  return (
    <div className="mx-auto flex min-h-screen max-w-7xl py-10">
      <div className="relative w-full overflow-hidden rounded-2xl shadow-xl">
        {/* <Image src={shipImg} alt="Ship" fill className="object-cover" />
        <div className="absolute inset-0 bg-black/50" /> */}

        <video
          autoPlay
          muted
          playsInline
          className="absolute inset-0 h-full w-full object-cover object-[60%_center]"
        >
          <source src="/intro.mp4" type="video/mp4" />
        </video>
        <div className="absolute inset-0 bg-black/10" />

        <div className="relative z-10 flex h-full w-full flex-col items-center justify-end p-12 text-center text-white">
          <h1 className="mb-2 text-xs font-bold uppercase tracking-wide">
            ONE PLATFORM FOR ALL PROJECT DOCUMENTATION
          </h1>
          <h2 className="mb-2 text-3xl">
            Clarity, Control, <br /> Collaboration
          </h2>
          <p className="mb-10 text-xs text-gray-200">
            The Most Reliable Platform for Seamless Project File Management
          </p>
        </div>
      </div>

      <div className="flex w-full items-center justify-center bg-white">{FormComponent}</div>
    </div>
  );
}
