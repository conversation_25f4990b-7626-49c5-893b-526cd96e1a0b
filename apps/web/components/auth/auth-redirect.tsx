"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Session from "supertokens-auth-react/recipe/session";
import Loading from "@repo/ui/components/loading";

export default function AuthRedirect({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const isLoggedIn = await Session.doesSessionExist();
        if (isLoggedIn) {
          router.replace("/dashboard");
          // Keep loading true to prevent flash of content before redirect
          return;
        }
        setIsLoading(false);
      } catch (error) {
        console.error("Auth check failed:", error);
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  if (isLoading) {
    return <Loading />;
  }

  return <>{children}</>;
}
