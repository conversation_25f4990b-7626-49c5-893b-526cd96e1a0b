"use client";
import React, { PropsWithChildren } from "react";
import { EmailVerificationClaim } from "supertokens-auth-react/recipe/emailverification";
import { SessionAuth, useSessionContext } from "supertokens-auth-react/recipe/session";
import { usePathname, useRouter } from "next/navigation";

const SuperTokenSessionAuthWrapper: React.FC<PropsWithChildren> = ({ children }) => {
  return <SessionAuth>{children}</SessionAuth>;
};

export default SuperTokenSessionAuthWrapper;
