# Email Verification Components

This directory contains reusable components for handling email verification in your SuperTokens-powered application.

## Components

### 1. `SendVerificationEmailButton`

A customizable button component that sends email verification emails with built-in loading states, error handling, and cooldown periods.

#### Props

```typescript
interface SendVerificationEmailButtonProps {
  variant?: "primary" | "secondary" | "outline";
  size?: "sm" | "md" | "lg";
  className?: string;
  children?: React.ReactNode;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}
```

#### Usage

```tsx
import { SendVerificationEmailButton } from "@/components/auth";

// Basic usage
<SendVerificationEmailButton>
  Send Verification Email
</SendVerificationEmailButton>

// With callbacks
<SendVerificationEmailButton
  variant="primary"
  size="md"
  onSuccess={() => console.log("Email sent successfully!")}
  onError={(error) => console.error("Failed to send email:", error)}
>
  Resend Verification
</SendVerificationEmailButton>
```

#### Features

- **Cooldown Period**: 60-second cooldown to prevent spam
- **Loading States**: Shows spinner and "Sending..." text during API calls
- **Error Handling**: Displays user-friendly error messages
- **Multiple Variants**: Primary, secondary, and outline styles
- **Responsive Sizes**: Small, medium, and large button sizes
- **Custom Styling**: Accepts additional CSS classes

### 2. `EmailVerificationBanner`

A banner component that automatically appears for users with unverified email addresses.

#### Usage

```tsx
import { EmailVerificationBanner } from "@/components/auth";

// Add to your dashboard or any protected page
<EmailVerificationBanner />
```

#### Features

- **Automatic Detection**: Only shows for unverified users
- **Dismissible**: Users can dismiss the banner
- **Integrated Button**: Includes a send verification email button
- **Clean Design**: Yellow warning banner with clear messaging

### 3. `VerifyEmailPage`

Enhanced email verification page with improved error handling and resend functionality.

#### Features

- **Token Validation**: Processes verification tokens from URL parameters
- **Smart Redirects**: Redirects based on user authentication status
- **Resend Functionality**: Allows users to request new verification emails
- **Error Recovery**: Provides options when verification fails

## Setup

### 1. Install Dependencies

Make sure you have SuperTokens React installed:

```bash
npm install supertokens-auth-react
```

### 2. Import Components

```tsx
import { 
  SendVerificationEmailButton, 
  EmailVerificationBanner,
  VerifyEmailPage 
} from "@/components/auth";
```

### 3. Use in Your App

#### Dashboard Integration

```tsx
// app/(protected)/dashboard/page.tsx
import { EmailVerificationBanner } from "@/components/auth";

export default function DashboardPage() {
  return (
    <div>
      <EmailVerificationBanner />
      {/* Your dashboard content */}
    </div>
  );
}
```

#### Profile Page Integration

```tsx
// components/profile/email-section.tsx
import { SendVerificationEmailButton } from "@/components/auth";

export default function EmailSection({ user }) {
  return (
    <div>
      <p>Email: {user.email}</p>
      {!user.emailVerified && (
        <SendVerificationEmailButton variant="outline" size="sm">
          Verify Email
        </SendVerificationEmailButton>
      )}
    </div>
  );
}
```

## API Reference

### SuperTokens Functions Used

- `sendVerificationEmail()`: Sends verification email to the current user
- `isEmailVerified()`: Checks if the current user's email is verified
- `verifyEmail()`: Verifies email using token from URL

### Error Handling

All components handle SuperTokens errors gracefully:

- **EMAIL_ALREADY_VERIFIED_ERROR**: Redirects to dashboard
- **General Errors**: Shows user-friendly error messages
- **Network Errors**: Provides retry options

## Styling

Components use Tailwind CSS classes and can be customized:

```tsx
<SendVerificationEmailButton
  className="w-full bg-custom-blue hover:bg-custom-blue-dark"
  variant="primary"
>
  Custom Styled Button
</SendVerificationEmailButton>
```

## Testing

Visit `/email-verification-demo` to see all components in action and test their functionality.

## Best Practices

1. **Always include EmailVerificationBanner** on protected pages
2. **Use appropriate button variants** based on context
3. **Implement success/error callbacks** for better UX
4. **Test with different user states** (verified/unverified)
5. **Customize styling** to match your app's design system
