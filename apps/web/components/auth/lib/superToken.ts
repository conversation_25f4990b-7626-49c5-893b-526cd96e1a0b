"use client";

import SuperTokensReact from "supertokens-auth-react";
import EmailPasswordReact from "supertokens-auth-react/recipe/emailpassword";
import SessionReact from "supertokens-auth-react/recipe/session";
import EmailVerification from "supertokens-auth-react/recipe/emailverification";

let initialized = false;

export function initSuperTokens() {
  // Only initialize on the client side
  if (typeof window !== "undefined" && !initialized) {
    SuperTokensReact.init({
      appInfo: {
        appName: "YourApp",
        apiDomain: "http://localhost:3005", // your backend
        websiteDomain: "http://localhost:3000", // your frontend
        apiBasePath: "/auth",
        websiteBasePath: "/auth",
      },
      recipeList: [
        EmailPasswordReact.init(),
        SessionReact.init(),
        EmailVerification.init({
          mode: "REQUIRED",
          emailVerificationFeature: {
            disableDefaultUI: false,
          },
        }),
      ],
    });
    initialized = true;
  }
}
