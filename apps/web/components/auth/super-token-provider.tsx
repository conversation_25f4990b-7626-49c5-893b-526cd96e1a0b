// components/SuperTokensProvider.tsx
"use client";

import { ReactNode, useEffect, useState } from "react";
import { initSuperTokens } from "./lib/superToken";
import Loading from "@repo/ui/components/loading";

export default function SuperTokensProvider({ children }: { children: ReactNode }) {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    // Initialize SuperTokens on the client side only
    if (typeof window !== "undefined") {
      try {
        console.log("Initializing SuperTokens...");
        initSuperTokens();
        console.log("SuperTokens initialized successfully");
        // Give a small delay to ensure initialization is complete
        setTimeout(() => {
          setIsReady(true);
        }, 100);
      } catch (error) {
        console.error("Failed to initialize SuperTokens:", error);
        // Even if initialization fails, we should still render the app
        // to avoid infinite loading
        setTimeout(() => {
          setIsReady(true);
        }, 1000);
      }
    } else {
      // If we're on server side, just set ready to true
      setIsReady(true);
    }

    // Failsafe: if initialization takes too long, proceed anyway
    const timeout = setTimeout(() => {
      console.warn("SuperTokens initialization timeout - proceeding anyway");
      setIsReady(true);
    }, 3000);

    return () => clearTimeout(timeout);
  }, []);

  // Show loading until SuperTokens is ready
  if (!isReady) {
    return <Loading />;
  }

  return <>{children}</>;
}
