"use client";
import React from "react";
import { Button } from "@repo/ui/components/button";
import { useRouter } from "next/navigation";
import { Icon, Icons } from "@repo/ui/components/icons";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";

interface NavButton {
  name: string;
  onClick: () => void;
}

interface SubNavBarProps {
  buttons?: NavButton[];
  actionButton: NavButton[]; // optional right-side button
}

const MENU_ITEMS = [
  {
    title: "Configurations",
    icon: Icons.workFlow,
    path: "/profile",
  },
  {
    title: "Glossary",
    icon: Icons.ChartBarIncreasing,
    path: "/glossary",
  },
];

export const SubNavBar: React.FC<SubNavBarProps> = ({ buttons, actionButton }) => {
  const router = useRouter();
  return (
    <div className="flex w-full items-center justify-between py-2">
      {/* Left Nav Buttons */}
      <div className="flex items-center gap-3">
        <Button
          key="dashboard"
          onClick={() => console.log("dashboard")}
          className="border-[#f0f7f6] bg-white text-black"
        >
          Dashboard
        </Button>
        <Button
          key="project"
          onClick={() => router.push("/project")}
          className="border-[#f0f7f6] bg-white text-black"
        >
          Project
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger className="flex items-center gap-2 rounded-md bg-white px-3 py-2 text-sm font-medium text-black shadow-sm hover:bg-gray-50">
            Tools
            <Icons.chevronDown className="h-4 w-4 text-gray-600" />
          </DropdownMenuTrigger>

          <DropdownMenuContent>
            {MENU_ITEMS.map(({ title, icon: Icon, path }) => (
              <DropdownMenuItem
                key={title}
                className="cursor-pointer"
                onClick={() => router.push(path)}
              >
                <div className="flex items-start gap-3 p-2">
                  <Icon className="size-5 text-teal-500" />
                  <div className="flex flex-col">
                    <h1 className="text-sm font-medium text-gray-900">{title}</h1>
                  </div>
                </div>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Right Action Button */}
      <div className="flex items-center gap-3">
        {actionButton?.length > 0 &&
          actionButton.map((item, index) => (
            <Button key={index} onClick={item.onClick} className="bg-primary text-white">
              {item.name}
            </Button>
          ))}
      </div>
    </div>
  );
};
