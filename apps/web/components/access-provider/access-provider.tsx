"use client";
import { all } from "axios";
import React, { createContext, ReactNode } from "react";
import { useMetaData } from "../meta-data-provider";

export type Permissions = {
  resource: Record<string, string[]>;
};

export interface AccessContextValue {
  permissions: Permissions;
}

interface AccessProviderProps {
  children: ReactNode;
}

export const AccessContext = createContext<AccessContextValue | null>(null);

// Custom hook to use AccessContext
export function useAccessContext() {
  const context = React.useContext(AccessContext);
  if (!context) {
    throw new Error("useAccessContext must be used within an AccessProvider");
  }
  return context;
}

// Utility: check if action is allowed
export function hasAccess(permissions: Permissions, resource: string, action: string): boolean {
  const allowedActions = permissions.resource?.[resource];
  if (!allowedActions) return false;

  // if (allowedActions.includes("all")) return true;
  return allowedActions.includes(action);
}

const AccessProvider: React.FC<AccessProviderProps> = ({ children }) => {
  // TODO: In a real app, you would fetch permissions from an API
  // For now, we'll use static permissions

  const metadata = useMetaData();
  console.log(metadata?.metaData?.user?.previlages);

  const permissions: Permissions = metadata?.metaData?.user?.previlages;

  return <AccessContext.Provider value={{ permissions }}>{children}</AccessContext.Provider>;
};

export default AccessProvider;
