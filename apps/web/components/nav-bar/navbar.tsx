"use client";

import React from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import dayjs from "dayjs";
import updateLocale from "dayjs/plugin/updateLocale";

import logo from "../../public/logo.png";
import personPic from "../../public/person.png";

import { Icons } from "@repo/ui/components/icons";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";

import { signOut } from "supertokens-auth-react/recipe/emailpassword";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { useMetaData } from "../meta-data-provider";
import { CanAccess } from "@/access-control";
import { Button } from "@repo/ui/components/button";

dayjs.extend(updateLocale);
dayjs.locale("en");

// ✅ Menu configuration outside component (no re-creation on every render)
const MENU_ITEMS = [
  {
    title: "Profile",
    description: "View and edit your personal details and profile settings.",
    icon: Icons.user,
    path: "/profile",
  },
  {
    title: "Settings",
    description: "Manage your account settings, theme, and notifications.",
    icon: Icons.settings,
    path: "/settings",
  },
  {
    title: "My Activity",
    description: "See recent activity or open projects you've worked on.",
    icon: Icons.history,
    path: "/activity",
  },
  {
    title: "User Management",
    description: "Manage users, roles, and permissions.",
    icon: Icons.users,
    path: "/users",
  },
  {
    title: "Tenant Management",
    description: "Manage tenants, roles, and permissions.",
    icon: Icons.boxes,
    path: "/tenant",
  },
  {
    title: "Help & Support",
    description: "Get help, view documentation, or contact support.",
    icon: Icons.headset,
    path: "/help",
  },
  {
    title: "Logout",
    description: "Securely sign out of your account.",
    icon: Icons.logout,
    path: "logout", // special key
  },
];

const Navbar: React.FC = () => {
  const router = useRouter();

  const { metaData } = useMetaData();

  console.log("metadata", metaData);

  const { user } = metaData;

  const handleRoute = async (path: string) => {
    if (path === "logout") {
      console.log("hitting", path);
      try {
        await signOut();
        router.push("/auth");
      } catch (err) {
        console.error("Logout failed:", err);
      }
    } else {
      router.push(path);
    }
  };

  return (
    <nav className="flex h-[80px] shrink-0 border bg-white px-4">
      <div className="flex w-full items-center justify-between px-4">
        {/* Left: Logo */}
        <Image src={logo} alt="Cadetlabs" className="h-[49px] w-[56px] object-contain" priority />

        {/* Right: Actions */}

        <div className="flex items-center space-x-6">
          <Select>
            <SelectTrigger className="h-10 w-[200px]">
              <SelectValue placeholder="Select an option" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="option1">Option 1</SelectItem>
              <SelectItem value="option2">Option 2</SelectItem>
              <SelectItem value="option3">Option 3</SelectItem>
            </SelectContent>
          </Select>

          {/* Divider */}
          <span className="h-6 w-px bg-gray-300" />

          {/* User Profile */}
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              {/* Profile Image */}
              <div className="h-[43px] w-[44px] overflow-hidden rounded-md">
                <Image src={personPic} alt="Profile" className="h-full w-full object-cover" />
              </div>

              {/* User Info + Dropdown */}
              <div className="leading-tight">
                <h1 className="text-sm font-medium text-gray-800">{user?.displayName || "NA"}</h1>
                <p className="text-[11px] text-gray-500">{user?.role || "NA"}</p>
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger>
                  <Icons.chevronDown className="size-4 text-gray-500" />
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-[300px]"
                  side="bottom"
                  align="end"
                  sideOffset={35}
                >
                  {MENU_ITEMS.map(({ title, description, icon: Icon, path }) => (
                    <Can
                    <DropdownMenuItem
                      key={title}
                      className="cursor-pointer"
                      onClick={() => handleRoute(path)}
                    >
                      <div className="flex items-start gap-3 p-2">
                        <Icon className="size-7 text-teal-500" />
                        <div className="flex flex-col">
                          <h1 className="text-sm font-medium text-gray-900">{title}</h1>
                          <p className="text-xs text-gray-500">{description}</p>
                        </div>
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Notification + Settings */}
            <div className="flex items-center space-x-3">
              {[Icons.bell, Icons.settings].map((Icon, i) => (
                <div
                  key={i}
                  className="flex h-9 w-9 cursor-pointer items-center justify-center rounded-full border border-gray-200 transition hover:bg-gray-100"
                >
                  <Icon className="size-4 text-gray-600" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
