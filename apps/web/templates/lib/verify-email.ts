import fs from "fs";
import path from "path";
import handlebars from "handlebars";

export const renderVerifyEmailTemplate = (data: {
  firstName: string;
  verifyUrl: string;
  logoUrl: string;
  year: number;
}) => {
  const templatePath = path.resolve("../../templates/verify-email.hbs");
  const source = fs.readFileSync(templatePath, "utf-8");
  const template = handlebars.compile(source);
  return template(data);
};
