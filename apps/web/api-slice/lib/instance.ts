import axios from "axios";

// process.env.NEXT_PUBLIC_BASE_URL

const instance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3005/api",
  headers: {
    "Content-Type": "application/json",
  },
});

const authinstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3005/auth",
  headers: {
    "Content-Type": "application/json",
  },
});

export { authinstance };

export default instance;
