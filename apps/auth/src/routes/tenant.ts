// routes/tenantRoutes.js
import { Router } from "express";
import { TenantController } from "../controllers/tenantController";

export const createTenantRouter = (): Router => {
  const router = Router();

  // GET all tenants
  router.get("/", TenantController.getAllTenants);

  // GET tenant by ID
  router.get("/:id", TenantController.getTenantById);

  // GET users by tenant
  router.get("/:tenantId/users", TenantController.getUsersByTenant);

  // POST create new tenant
  router.post("/", TenantController.createTenant);

  // PUT update tenant
  router.put("/:id", TenantController.updateTenant);

  // DELETE tenant
  router.delete("/:id", TenantController.deleteTenant);

  router.post("/list", TenantController.listTenants);

  return router;
};
