// routes/tenantRoutes.js
import { Router } from "express";
import { TenantController } from "../controllers/tenantController";
import { rbacMiddleware } from "../middleware/rbac";

export const createTenantRouter = (): Router => {
  const router = Router();

  // GET all tenants
  router.get("/", rbacMiddleware("tenant", "read"), TenantController.getAllTenants);

  // GET tenant by ID
  router.get("/:id", rbacMiddleware("tenant", "read"), TenantController.getTenantById);

  // GET users by tenant
  router.get(
    "/:tenantId/users",
    rbacMiddleware("tenant", "read"),
    TenantController.getUsersByTenant
  );

  // POST create new tenant
  router.post("/", rbacMiddleware("tenant", "create"), TenantController.createTenant);

  // PUT update tenant
  router.put("/:id", rbacMiddleware("tenant", "update"), TenantController.updateTenant);

  // DELETE tenant
  router.delete("/:id", rbacMiddleware("tenant", "delete"), TenantController.deleteTenant);

  router.post("/list", rbacMiddleware("tenant", "read"), TenantController.listTenants);

  return router;
};
