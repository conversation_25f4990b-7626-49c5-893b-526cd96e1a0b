import { Router } from "express";
import { verifySession } from "supertokens-node/recipe/session/framework/express";

// Import all controllers
import { AuthController } from "../controllers/authController";
import { UserController } from "../controllers/userController";
import { EmailController } from "../controllers/emailController";
import { PasswordController } from "../controllers/passwordController";
import { SessionController } from "../controllers/sessionContoller";
import { HealthController } from "../controllers/healthController";
import { MetadataController } from "../controllers/metadataController";

export const createAuthRouter = () => {
  const router = Router();

  // Health check routes
  router.get("/health", HealthController.healthCheck);

  // Authentication routes
  router.post("/signin", AuthController.signIn);
  router.post("/signup", AuthController.signUp);
  router.post("/signout", verifySession(), AuthController.signOut);
  router.post("/get-metadata-by-email", MetadataController.getMetadataByEmail);
  router.post("/set-metadata-by-email", MetadataController.setMetadataByEmail);

  // User management routes
  router.get("/me", verifySession(), UserController.getCurrentUser);
  // router.get("/user", verifySession(), UserController.getUserById);
  router.post("/create-user", UserController.createUser);
  router.get("/signup/email/exists", UserController.checkEmailExists);
  router.put("/user/metadata", verifySession(), UserController.updateUserMetadata);
  router.delete("/user/metadata", verifySession(), UserController.clearUserMetadata);
  // router.get(
  //   "/user/email/verify",

  //   EmailController.checkEmailVerificationStatus
  // );

  // Email management routes
  router.post("/user/email/verify/send", EmailController.sendEmailVerification);
  router.post("/user/email/verify", EmailController.verifyEmailToken);
  // router.get("/email/verify/status", verifySession(), EmailController.checkEmailVerificationStatus);
  // router.post("/email/verify/resend", verifySession(), EmailController.resendEmailVerification);
  // router.post("/email/verify/token", EmailController.verifyEmailToken);
  // Password management routes
  router.post("/user/password/reset/token", PasswordController.sendPasswordResetEmail);
  // router.post("/user/password/reset", PasswordController.resetPassword);
  // router.post("/verify-reset-token", PasswordController.verifyResetToken);
  // router.put("/user/password/change", verifySession(), PasswordController.changePassword);

  // Session management routes
  // router.post("/generate-token", SessionController.generateSessionToken);
  // router.get("/session/info", verifySession(), SessionController.getSessionInfo);
  // router.put("/session/data", verifySession(), SessionController.updateSessionData);
  // router.put("/session/payload", verifySession(), SessionController.updateAccessTokenPayload);
  // router.post("/session/refresh", verifySession(), SessionController.refreshSession);
  // router.get("/session/user", verifySession(), SessionController.getUserSessions);
  // router.delete("/session/all", verifySession(), SessionController.revokeAllSessions);
  // router.delete("/session", SessionController.revokeSession);

  return router;
};
