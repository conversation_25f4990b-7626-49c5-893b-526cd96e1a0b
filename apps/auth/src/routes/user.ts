// routes/userRoutes.js
import { Router } from "express";
import { StaffUserController } from "../controllers/staffUserController";
import { rbacMiddleware } from "../middleware/rbac";

export const createUserRouter = (): Router => {
  const router = Router();

  // GET all users
  router.get("/", rbacMiddleware("user", "read"), StaffUserController.getAllUsers);

  // GET user by ID
  router.get("/:id", rbacMiddleware("user", "read"), StaffUserController.getUserById);

  // POST create new user
  router.post("/", rbacMiddleware("user", "create"), StaffUserController.createUser);

  // PUT update user
  router.put("/:id", rbacMiddleware("user", "update"), StaffUserController.updateUser);

  router.post("/list", rbacMiddleware("user", "read"), StaffUserController.listUsers);

  router.delete("/:id", rbacMiddleware("user", "delete"), StaffUserController.deleteUser);

  router.put("/:id/activate", StaffUserController.activateUser);
  router.put("/:id/deactivate", StaffUserController.deactivateUser);
  router.post("/:id/resend-email-verification", StaffUserController.resendEmailVerification);

  return router;
};
