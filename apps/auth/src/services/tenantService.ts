import { getPrisma } from "@repo/database";
import { StaffUserService } from "./StaffUserService.js";

export class TenantService {
  // Helper function to generate tenant ID
  static generateTenantId() {
    return `tenant_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  // Get all tenants
  static async getAllTenants() {
    const prisma = getPrisma();

    const tenants = await prisma.tenant.findMany({
      include: {
        users: true, // Include related users
      },
    });

    return tenants;
  }

  // Get tenant by ID
  static async getTenantById(tenantId) {
    if (!tenantId) {
      const error = new Error("Tenant ID is required");
      error.statusCode = 400;
      throw error;
    }

    const prisma = getPrisma();

    const tenant = await prisma.tenant.findUnique({
      where: { id: tenantId },
      include: {
        users: true,
      },
    });

    if (!tenant) {
      const error = new Error("Tenant not found");
      error.statusCode = 404;
      throw error;
    }

    return tenant;
  }

  // Get users by tenant
  static async getUsersByTenant(tenantId) {
    if (!tenantId) {
      const error = new Error("Tenant ID is required");
      error.statusCode = 400;
      throw error;
    }

    const prisma = getPrisma();

    const users = await prisma.user.findMany({
      where: { tenantId: tenantId },
      include: {
        tenant: true,
      },
    });

    return users;
  }

  // Create new tenant
  static async createTenant(tenantData) {
    const { companyName, adminName, emailId, subscriptionType, contactNo, capOnUsers, address } =
      tenantData;

    // Basic validation
    if (
      !companyName ||
      !adminName ||
      !emailId ||
      !subscriptionType ||
      !contactNo ||
      !capOnUsers ||
      !address
    ) {
      const error = new Error("All fields are required");
      error.statusCode = 400;
      throw error;
    }

    const prisma = getPrisma();

    try {
      const tenant = await prisma.tenant.create({
        data: {
          tenantId: TenantService.generateTenantId(),
          companyName,
          adminName,
          emailId,
          subscriptionType,
          contactNo: contactNo,
          capOnUsers: parseInt(capOnUsers),
          address,
        },
      });
      StaffUserService.createUser(
        {
          firstName: adminName,
          lastName: adminName,
          emailId: emailId,
          role: "admin",
          tenantId: tenant.tenantId,
        },
        {
          tenantId: tenant.tenantId,
        }
      );
      return tenant;
    } catch (error) {
      if (error.code === "P2002") {
        const customError = new Error("Email already exists");
        customError.statusCode = 400;
        throw customError;
      }
      throw error;
    }
  }

  // Update tenant
  static async updateTenant(tenantId, updateData) {
    if (!tenantId) {
      const error = new Error("Tenant ID is required");
      error.statusCode = 400;
      throw error;
    }

    // Convert contactNo to BigInt if provided
    if (updateData.contactNo) {
      updateData.contactNo = BigInt(updateData.contactNo);
    }

    // Convert capOnUsers to int if provided
    if (updateData.capOnUsers) {
      updateData.capOnUsers = parseInt(updateData.capOnUsers);
    }

    const prisma = getPrisma();

    try {
      const tenant = await prisma.tenant.update({
        where: { id: tenantId },
        data: updateData,
      });

      return tenant;
    } catch (error) {
      if (error.code === "P2025") {
        const customError = new Error("Tenant not found");
        customError.statusCode = 404;
        throw customError;
      } else if (error.code === "P2002") {
        const customError = new Error("Email already exists");
        customError.statusCode = 400;
        throw customError;
      }
      throw error;
    }
  }

  // Delete tenant
  static async deleteTenant(tenantId) {
    if (!tenantId) {
      const error = new Error("Tenant ID is required");
      error.statusCode = 400;
      throw error;
    }

    const prisma = getPrisma();

    try {
      // First get the tenant to get its tenantId
      const tenant = await prisma.tenant.findUnique({
        where: { id: tenantId },
      });

      if (!tenant) {
        const error = new Error("Tenant not found");
        error.statusCode = 404;
        throw error;
      }

      // Check if tenant has users
      const userCount = await prisma.user.count({
        where: { tenantId: tenant.tenantId },
      });

      if (userCount > 0) {
        const error = new Error("Cannot delete tenant with existing users. Delete users first.");
        error.statusCode = 400;
        throw error;
      }

      await prisma.tenant.delete({
        where: { id: tenantId },
      });

      return { message: "Tenant deleted successfully" };
    } catch (error) {
      if (error.code === "P2025") {
        const customError = new Error("Tenant not found");
        customError.statusCode = 404;
        throw customError;
      }
      throw error;
    }
  }

  static async listTenants({ limit = 10, cursor }: { limit?: number; cursor?: string } = {}) {
    const prisma = getPrisma();

    const take = Math.min(Math.max(1, Number(limit) || 10), 100);

    const query: any = {
      take: take + 1,
      orderBy: { createdAt: "desc" }, // better than UUID ordering
    };

    if (cursor) {
      query.cursor = { id: cursor }; // UUID works fine here
      query.skip = 1;
    }

    const results = await prisma.tenant.findMany(query);

    const hasMore = results.length > take;
    const tenants = results.slice(0, take);
    const nextCursor = hasMore ? tenants[tenants.length - 1].id : null;

    return {
      tenants,
      hasMore,
      nextCursor,
    };
  }
}
