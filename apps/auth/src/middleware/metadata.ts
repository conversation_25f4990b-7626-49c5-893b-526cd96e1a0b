import UserMetadata from "supertokens-node/recipe/usermetadata";

export const metadataMiddleware = async (req: any, res: any, next: any) => {
  try {
    const userId = req.session!.getUserId();
    const metadata = await UserMetadata.getUserMetadata(userId);
    req.metadata = metadata.metadata;
    next();
  } catch (err) {
    console.error("metadata middleware error:", err);
    res.status(500).json({ error: "Failed to fetch metadata" });
  }
};
