import { Request, Response } from "express";
import EmailPassword from "supertokens-node/recipe/emailpassword";
import { listUsersByAccountInfo } from "supertokens-node";
import EmailVerification from "supertokens-node/recipe/emailverification";
import { EmailService } from "../services/emailService.js";
import supertokens from "supertokens-node";
const TENANT_ID = process.env.SUPERTOKENS_TENANT_ID || "public";
const WEBSITE_DOMAIN = process.env.WEBSITE_DOMAIN || "http://localhost:3000";

export class PasswordController {
  /**
   * Send password reset email
   */
  static async sendPasswordResetEmail(req: Request, res: Response) {
    try {
      const formFields = req.body.formFields;
      console.log("signin-req", req.body);

      // Convert array into object: { email: "value", password: "value" }
      const fields = formFields.reduce((acc: any, field: any) => {
        acc[field.id] = field.value;
        return acc;
      }, {});

      const { email } = fields;

      if (!email) {
        return res.status(400).json({ error: "email is required" });
      }

      // Find the user by email
      const users = await listUsersByAccountInfo(TENANT_ID, { email });
      if (!users || users.length === 0) {
        // Don't leak whether account exists
        return res.json({
          status: "OK",
          note: "If the email exists, a reset message will be sent.",
        });
      }

      const user = users[0];
      const userId = user?.id as string;
      console.log(userId);
      let recipeUserId;
      if (
        user?.loginMethods &&
        user?.loginMethods?.length > 0 &&
        user?.loginMethods[0]?.recipeUserId
      ) {
        recipeUserId = user.loginMethods[0].recipeUserId;
      } else {
        // create one from the primary user id
        recipeUserId = new supertokens.RecipeUserId(user?.id as string);
      }
      const isVerified = await EmailVerification.isEmailVerified(recipeUserId, email);
      console.log("isVerified:", isVerified);
      if (!isVerified) {
        return res.json({
          status: "PASSWORD_RESET_NOT_ALLOWED",
          note: "Please verify your email before resetting your password.",
        });
      }

      // Create reset token
      const tokenResp = await EmailPassword.createResetPasswordToken(TENANT_ID, userId, email);
      if (tokenResp.status !== "OK") {
        console.error("createResetPasswordToken:", tokenResp);
        return res.status(500).json({ status: tokenResp.status });
      }

      const token = tokenResp.token;
      const resetLink = `${WEBSITE_DOMAIN}/auth/reset-password?token=${encodeURIComponent(token)}&rid=emailpassword`;

      EmailService.sendEmailWithTemplate(
        {
          to: email,
          subject: "Reset your password",
          data: {
            email,
            resetLink,
          },
        },
        "password-reset"
      );
      return res.json({ status: "OK" });
    } catch (err) {
      console.error("forgot-password error:", err);
      return res.status(500).json({ error: "Failed to send reset email" });
    }
  }

  /**
   * Reset password using reset token
   */
  static async resetPassword(req: Request, res: Response) {
    try {
      const formFields = req.body.formFields;
      const token = req.body.token;
      // Convert array into object: { email: "value", password: "value" }
      const fields = formFields.reduce((acc: any, field: any) => {
        acc[field.id] = field.value;
        return acc;
      }, {});

      const { password } = fields;

      if (!token || !password) {
        return res.status(400).json({ error: "token and newPassword are required" });
      }

      const resp = await EmailPassword.resetPasswordUsingToken(TENANT_ID, token, password);
      return res.json(resp);
    } catch (err) {
      console.error("reset-password error:", err);
      return res.status(500).json({ error: "Failed to reset password" });
    }
  }

  /**
   * Verify reset password token
   */
  static async verifyResetToken(req: Request, res: Response) {
    try {
      const { token } = req.body;

      const response = await EmailPassword.consumePasswordResetToken(token);

      if (response.status === "OK") {
        return res.json({ status: "OK", userId: response.userId });
      } else {
        return res.json({ status: "INVALID_TOKEN" });
      }
    } catch (err) {
      console.error(err);
      return res.status(500).json({ error: "Something went wrong" });
    }
  }

  /**
   * Change password for authenticated user
   */
  static async changePassword(req: Request, res: Response) {
    try {
      const session = req.session!;
      const userId = session.getUserId();
      const { currentPassword, newPassword } = req.body;

      if (!currentPassword || !newPassword) {
        return res.status(400).json({
          error: "currentPassword and newPassword are required",
        });
      }

      // Get user email first
      const userObj = await EmailPassword.getUserById(userId);
      const email = userObj?.emails?.[0];
      if (!email) {
        return res.status(400).json({ error: "User has no email" });
      }

      // Verify current password by attempting to sign in
      const signInResp = await EmailPassword.signIn("public", email, currentPassword);
      if (signInResp.status !== "OK") {
        return res.status(401).json({ error: "Current password is incorrect" });
      }

      // Update password
      const updateResp = await EmailPassword.updateEmailOrPassword({
        recipeUserId: signInResp.recipeUserId,
        password: newPassword,
      });

      if (updateResp.status === "OK") {
        return res.json({ status: "OK" });
      } else {
        return res.status(400).json({ status: updateResp.status });
      }
    } catch (err) {
      console.error("change password error:", err);
      return res.status(500).json({ error: "Failed to change password" });
    }
  }
}
