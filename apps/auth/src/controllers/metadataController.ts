import { Request, Response } from "express";
import { listUsersByAccountInfo } from "supertokens-node";
import EmailPassword from "supertokens-node/recipe/emailpassword";
import Session from "supertokens-node/recipe/session";
import UserMetadata from "supertokens-node/recipe/usermetadata";

export class MetadataController {
  static async getMetadataByEmail(req: Request, res: Response) {
    try {
      const { email } = req.body;

      // Input validation
      if (!email || typeof email !== "string") {
        return res.status(400).json({ error: "Valid email is required" });
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({ error: "Invalid email format" });
      }

      const users = await listUsersByAccountInfo("public", { email: email });
      if (!users || users.length === 0) {
        return;
      }
      //   const user = users[0];
      if (!users || users.length === 0) {
        return res.status(404).json({ error: "User not found" });
      }

      const userId = users[0]?.id;

      const metadata = await UserMetadata.getUserMetadata(userId as string);
      return res.json({
        success: true,
        metadata: metadata.metadata,
      });
    } catch (err) {
      console.error("get metadata error:", err);
      return res.status(500).json({ error: "Failed to fetch metadata" });
    }
  }

  static async setMetadataByEmail(req: Request, res: Response) {
    try {
      const { email, metadata } = req.body;

      // Input validation
      if (!email || typeof email !== "string") {
        return res.status(400).json({ error: "Valid email is required" });
      }

      if (!metadata || typeof metadata !== "object") {
        return res.status(400).json({ error: "Valid metadata object is required" });
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({ error: "Invalid email format" });
      }

      const users = await listUsersByAccountInfo("public", { email: email });
      if (!users || users.length === 0) {
        return;
      }
      //   const user = users[0];
      if (!users || users.length === 0) {
        return res.status(404).json({ error: "User not found" });
      }

      if (!users || users.length === 0) {
        return res.status(404).json({ error: "User not found" });
      }

      const userId = users[0]?.id;

      // Optional: Validate metadata structure/content
      // Add any business logic validation here

      await UserMetadata.updateUserMetadata(userId as string, metadata);

      return res.json({
        success: true,
        message: "Metadata updated successfully",
      });
    } catch (err) {
      console.error("set metadata error:", err);

      // Handle specific SuperTokens errors
      if (err.type === Session.Error.UNAUTHORISED) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      return res.status(500).json({ error: "Failed to set metadata" });
    }
  }

  // Alternative: Methods that work with userId directly (more secure)
  static async getMetadata(req: Request, res: Response) {
    try {
      const session = await Session.getSession(req, res);
      const userId = session.getUserId();

      const metadata = await UserMetadata.getUserMetadata(userId);
      return res.json({
        success: true,
        metadata: metadata.metadata,
      });
    } catch (err) {
      console.error("get metadata error:", err);

      if (err.type === Session.Error.UNAUTHORISED) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      return res.status(500).json({ error: "Failed to fetch metadata" });
    }
  }

  static async setMetadata(req: Request, res: Response) {
    try {
      const { metadata } = req.body;

      if (!metadata || typeof metadata !== "object") {
        return res.status(400).json({ error: "Valid metadata object is required" });
      }

      const session = await Session.getSession(req, res);
      const userId = session.getUserId();

      await UserMetadata.updateUserMetadata(userId, metadata);

      return res.json({
        success: true,
        message: "Metadata updated successfully",
      });
    } catch (err) {
      console.error("set metadata error:", err);

      if (err.type === Session.Error.UNAUTHORISED) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      return res.status(500).json({ error: "Failed to set metadata" });
    }
  }
}
