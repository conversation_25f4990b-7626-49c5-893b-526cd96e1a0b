import { Request, Response } from "express";
import Session from "supertokens-node/recipe/session";

export class SessionController {
  /**
   * Generate session token without request/response objects
   */
  static async generateSessionToken(req: Request, res: Response) {
    try {
      const { userId } = req.body;

      if (!userId) {
        return res.status(400).json({ error: "userId is required" });
      }

      // Create session without needing request/response objects
      const session = await Session.createNewSessionWithoutRequestResponse(
        "public",
        userId,
        {},
        {}
      );
      const tokens = session.getAllSessionTokensDangerously();

      return res.json({
        status: "OK",
        userId,
        tokens,
      });
    } catch (err) {
      console.error(err);
      return res.status(500).json({ error: "Failed to generate token" });
    }
  }

  /**
   * Get current session information
   */
  static async getSessionInfo(req: Request, res: Response) {
    try {
      const session = req.session!;

      return res.json({
        status: "OK",
        session: {
          userId: session.getUserId(),
          sessionHandle: session.getHandle(),
          accessTokenPayload: session.getAccessTokenPayload(),
          sessionDataInDatabase: await session.getSessionDataInDatabase(),
        },
      });
    } catch (err) {
      console.error("get session info error:", err);
      return res.status(500).json({ error: "Failed to get session info" });
    }
  }

  /**
   * Update session data
   */
  static async updateSessionData(req: Request, res: Response) {
    try {
      const session = req.session!;
      const { sessionData } = req.body;

      if (!sessionData) {
        return res.status(400).json({ error: "sessionData is required" });
      }

      await session.updateSessionDataInDatabase(sessionData);

      return res.json({ status: "OK" });
    } catch (err) {
      console.error("update session data error:", err);
      return res.status(500).json({ error: "Failed to update session data" });
    }
  }

  /**
   * Update access token payload
   */
  static async updateAccessTokenPayload(req: Request, res: Response) {
    try {
      const session = req.session!;
      const { payload } = req.body;

      if (!payload) {
        return res.status(400).json({ error: "payload is required" });
      }

      await session.mergeIntoAccessTokenPayload(payload);

      return res.json({ status: "OK" });
    } catch (err) {
      console.error("update access token payload error:", err);
      return res.status(500).json({ error: "Failed to update access token payload" });
    }
  }

  /**
   * Refresh session
   */
  static async refreshSession(req: Request, res: Response) {
    try {
      const session = req.session!;

      // The session is automatically refreshed by the middleware if needed
      // This endpoint just confirms the session is valid and returns info
      return res.json({
        status: "OK",
        message: "Session refreshed successfully",
        userId: session.getUserId(),
      });
    } catch (err) {
      console.error("refresh session error:", err);
      return res.status(500).json({ error: "Failed to refresh session" });
    }
  }

  /**
   * Get all sessions for a user
   */
  static async getUserSessions(req: Request, res: Response) {
    try {
      const session = req.session!;
      const userId = session.getUserId();

      const sessions = await Session.getAllSessionHandlesForUser(userId);

      return res.json({
        status: "OK",
        sessions: sessions,
      });
    } catch (err) {
      console.error("get user sessions error:", err);
      return res.status(500).json({ error: "Failed to get user sessions" });
    }
  }

  /**
   * Revoke all sessions for current user
   */
  static async revokeAllSessions(req: Request, res: Response) {
    try {
      const session = req.session!;
      const userId = session.getUserId();

      await Session.revokeAllSessionsForUser(userId);

      return res.json({ status: "OK" });
    } catch (err) {
      console.error("revoke all sessions error:", err);
      return res.status(500).json({ error: "Failed to revoke all sessions" });
    }
  }

  /**
   * Revoke specific session
   */
  static async revokeSession(req: Request, res: Response) {
    try {
      const { sessionHandle } = req.body;

      if (!sessionHandle) {
        return res.status(400).json({ error: "sessionHandle is required" });
      }

      const revoked = await Session.revokeSession(sessionHandle);

      return res.json({
        status: "OK",
        revoked: revoked,
      });
    } catch (err) {
      console.error("revoke session error:", err);
      return res.status(500).json({ error: "Failed to revoke session" });
    }
  }
}
