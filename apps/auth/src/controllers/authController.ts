import { Request, Response } from "express";
import EmailPassword from "supertokens-node/recipe/emailpassword";
import Session from "supertokens-node/recipe/session";
import UserMetadata from "supertokens-node/recipe/usermetadata";

export class AuthController {
  /**
   * Handle user sign in
   */
  static async signIn(req: Request, res: Response) {
    try {
      const formFields = req.body.formFields;
      console.log("signin-req", req.body);

      // Convert array into object: { email: "value", password: "value" }
      const fields = formFields.reduce((acc: any, field: any) => {
        acc[field.id] = field.value;
        return acc;
      }, {});

      const { email, password } = fields;

      const response = await EmailPassword.signIn("public", email, password);

      if (response.status === "OK") {
        const recipeUserId = response.recipeUserId;
        const user = response.user;

        // Create new session
        await Session.createNewSession(req, res, "public", recipeUserId, {}, {}, {});

        const metadata = await UserMetadata.getUserMetadata(user.id);

        return res.json({
          status: "OK",
          user: {
            id: user.id,
            email: user.emails[0],
            timeJoined: user.timeJoined,
            metadata: metadata.metadata,
          },
        });
      } else if (response.status === "WRONG_CREDENTIALS_ERROR") {
        return res.status(401).json({ status: "WRONG_CREDENTIALS_ERROR" });
      } else if (response.status === "EMAIL_NOT_VERIFIED_ERROR") {
        return res.status(403).json({ status: "EMAIL_NOT_VERIFIED_ERROR" });
      }
    } catch (err) {
      console.error(err);
      return res.status(500).json({ error: "Something went wrong" });
    }
  }

  /**
   * Handle user sign up
   */
  static async signUp(req: Request, res: Response) {
    try {
      const formFields = req.body.formFields;

      // Convert array into object
      const fields = formFields.reduce((acc: any, field: any) => {
        acc[field.id] = field.value;
        return acc;
      }, {});

      const { email, password, metadata } = fields;

      // Step 1: Create the user
      const response = await EmailPassword.signUp("public", email, password);

      if (response.status === "OK") {
        const user = response.user;

        // Step 2: Store metadata (if provided)
        if (metadata) {
          await UserMetadata.updateUserMetadata(user.id, metadata);
        }

        // Fetch full metadata again to return
        const fullMetadata = await UserMetadata.getUserMetadata(user.id);

        return res.json({
          status: "OK",
          user: {
            id: user.id,
            email: user.emails[0],
            timeJoined: user.timeJoined,
            metadata: fullMetadata.metadata,
          },
        });
      } else if (response.status === "EMAIL_ALREADY_EXISTS_ERROR") {
        return res.status(400).json({ status: "EMAIL_ALREADY_EXISTS_ERROR" });
      }
    } catch (err) {
      console.error(err);
      return res.status(500).json({ error: "Something went wrong" });
    }
  }

  /**
   * Handle user sign out
   */
  static async signOut(req: Request, res: Response) {
    try {
      await req.session!.revokeSession();
      return res.json({ status: "OK" });
    } catch (err) {
      console.error("signout error:", err);
      return res.status(500).json({ error: "Failed to sign out" });
    }
  }
}
