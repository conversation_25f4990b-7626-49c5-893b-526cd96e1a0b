import { TenantService } from "../services/tenantService";

export class TenantController {
  // Get all tenants
  static async getAllTenants(req, res) {
    try {
      const tenants = await TenantService.getAllTenants();
      res.json(tenants);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  // Get tenant by ID
  static async getTenantById(req, res) {
    try {
      const tenant = await TenantService.getTenantById(req.params.id);
      res.json(tenant);
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }

  // Get users by tenant
  static async getUsersByTenant(req, res) {
    try {
      const users = await TenantService.getUsersByTenant(req.params.tenantId);
      res.json(users);
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }

  // Create new tenant
  static async createTenant(req, res) {
    try {
      const tenant = await TenantService.createTenant(req.body);
      res.status(201).json({ message: "successfully created", tenant });
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }

  // Update tenant
  static async updateTenant(req, res) {
    try {
      const tenant = await TenantService.updateTenant(req.params.id, req.body);
      res.json(tenant);
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }

  // Delete tenant
  static async deleteTenant(req, res) {
    try {
      const result = await TenantService.deleteTenant(req.params.id);
      res.json(result);
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }

  static async listTenants(req, res) {
    try {
      const { limit, cursor } = req.body;
      const result = await TenantService.listTenants({ limit, cursor });
      res.json(result);
    } catch (error) {
      const statusCode = error.statusCode || 500;
      res.status(statusCode).json({ error: error.message });
    }
  }
}
