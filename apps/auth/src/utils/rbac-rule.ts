export const rbacRules = {
  superadmin: {
    resource: {
      tenant: ["all", "create", "update", "delete", "read"],
      user: ["all", "create", "update", "delete", "read"],
      project: ["all", "create", "update", "delete", "read"],
    },
  },
  admin: {
    resource: {
      user: ["all", "create", "update", "delete", "read"],
      project: ["all", "create", "update", "delete", "read"],
    },
  },
  project_handler: {
    resource: {
      project: ["all", "create", "update", "delete", "read"],
    },
  },
};
