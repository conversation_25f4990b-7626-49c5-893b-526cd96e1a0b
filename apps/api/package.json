{"name": "api", "version": "0.0.0", "type": "module", "private": true, "scripts": {"start": "node dist/index.cjs", "dev": "tsup --watch --onSuccess \"node dist/index.cjs\"", "build": "tsup", "check-types": "tsc --noEmit", "lint": "eslint src/ --max-warnings 0", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jest": {"preset": "@repo/jest-presets/node"}, "dependencies": {"@repo/logger": "*", "body-parser": "^1.20.3", "cors": "^2.8.5", "express": "4.21.2", "morgan": "^1.10.0"}, "devDependencies": {"@jest/globals": "^29.7.0", "@repo/eslint-config": "*", "@repo/jest-presets": "*", "@repo/typescript-config": "*", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "4.17.21", "@types/morgan": "^1.9.9", "@types/node": "^22.15.3", "@types/supertest": "^6.0.2", "eslint": "^9.31.0", "jest": "^29.7.0", "supertest": "^7.1.0", "tsup": "^8.5.0", "typescript": "5.8.2"}}