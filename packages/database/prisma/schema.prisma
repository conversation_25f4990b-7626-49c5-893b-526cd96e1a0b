// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["app"]
}

model Tenant {
  id               String   @id @default(uuid()) @db.Uuid
  tenantId         String   @unique @map("tenant_id") @db.VarChar(255)
  companyName      String   @map("company_name") @db.VarChar(255)
  adminName        String   @map("admin_name") @db.VarChar(255)
  emailId          String   @unique @map("email_id") @db.VarChar(255)
  subscriptionType String   @map("subscription_type") @db.VarChar(50)
  contactNo        String   @map("contact_no") @db.VarChar(15)
  capOnUsers       Int      @map("cap_on_users") @db.Integer
  address          String   @db.Text
  createdAt        DateTime @default(now()) @map("created_at") @db.Timestamp
  updatedAt        DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamp
  first_time_user Boolean @default(true) @map("first_time_user") @db.Boolean

  // Relations
  users User[]

  @@map("tenant")
  @@schema("app")
}

model User {
  id           String    @id @default(uuid()) @db.Uuid
  firstName    String    @map("first_name") @db.VarChar(100)
  lastName     String?   @map("last_name") @db.VarChar(100)
  displayName  String    @map("display_name") @db.VarChar(200)
  emailId      String    @unique @map("email_id") @db.VarChar(255)
  role         String    @db.VarChar(50)
  isActive     Boolean   @default(true) @map("is_active") @db.Boolean
  tenantId     String    @map("tenant_id") @db.VarChar(255)
  createdAt    DateTime  @default(now()) @map("created_at") @db.Timestamp
  updatedAt    DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamp
  lastLoginAt  DateTime? @map("last_login_at") @db.Timestamp
  first_time_user Boolean @default(true) @map("first_time_user") @db.Boolean

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [tenantId])

  @@map("user")
  @@schema("app")
}