{"name": "@repo/database", "version": "0.0.0", "private": true, "files": ["dist"], "main": "./dist/es/index.js", "module": "./dist/es/index.js", "types": "./dist/es/index.d.ts", "exports": {".": {"import": {"types": "./dist/es/index.d.ts", "default": "./dist/es/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}}, "scripts": {"build": "bunchee", "dev": "bunchee --watch", "check-types": "tsc src/index.ts --noEmit", "lint": "eslint src/ --max-warnings 0", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:reset": "prisma migrate reset"}, "dependencies": {"@prisma/client": "^5.20.0", "pg": "^8.16.3", "prisma": "^5.20.0"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/typescript-config": "*", "@types/node": "^22.15.3", "@types/pg": "^8.15.5", "eslint": "^9.31.0", "tsup": "^8.5.0", "bunchee": "^6.4.0", "typescript": "5.8.2"}}