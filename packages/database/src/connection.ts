// db.ts
import { PrismaClient } from "@prisma/client";

export interface DatabaseConfig {
  user: string;
  password: string;
  host: string;
  port: number;
  database: string;
  schema?: string; // optional, defaults to "public"
}

let prisma: PrismaClient | null = null;

/**
 * Build the PostgreSQL connection string dynamically
 */
const buildConnectionString = (config: DatabaseConfig): string => {
  const schema = config.schema || "public";
  return `postgresql://${encodeURIComponent(config.user)}:${encodeURIComponent(
    config.password
  )}@${config.host}:${config.port}/${config.database}?schema=${schema}`;
};

/**
 * Initialize Prisma client with dynamic config
 */
export const initializeDatabase = async (config: DatabaseConfig): Promise<void> => {
  try {
    const url = buildConnectionString(config);

    prisma = new PrismaClient({
      datasources: {
        db: {
          url,
        },
      },
    });

    await prisma.$connect();
    console.log("✅ Database connected successfully");
  } catch (error) {
    console.error("❌ Failed to connect to database:", error);
    throw error;
  }
};

/**
 * Gracefully disconnect Prisma
 */
export const disconnectDatabase = async (): Promise<void> => {
  if (!prisma) return;
  try {
    await prisma.$disconnect();
    console.log("✅ Database disconnected successfully");
  } catch (error) {
    console.error("❌ Failed to disconnect from database:", error);
    throw error;
  }
};

/**
 * Get Prisma client instance (after init)
 */
export const getPrisma = (): PrismaClient => {
  if (!prisma) {
    throw new Error("Prisma client not initialized. Call initializeDatabase first.");
  }
  return prisma;
};
